# Tekla 构件比较工具改进说明

## 改进内容

### 1. UI界面改进
- 启用了"差异数据"列，直接显示具体的数值差异
- 调整窗口宽度至504px，以容纳更多信息
- 差异数据列宽度设置为200px

### 2. 差异数据显示格式
现在差异数据列会直接显示具体数值，使用 `|` 分隔符分割两个构件的对应数据：

#### 显示格式示例：
- **截面差异**: `HW200x200x8x12|HW200x200x10x16`
- **材质差异**: `Q235|Q345`
- **长度差异**: `6000.0|6100.0`
- **重量差异**: `125.5kg|130.2kg`
- **位置差异**: `(1000.0,2000.0,3000.0)|(1000.0,2000.0,3100.0)`

#### 多个差异组合显示：
如果构件有多个差异，会用 ` ; ` 分隔：
```
HW200x200x8x12|HW200x200x10x16 ; Q235|Q345 ; (1000.0,2000.0,3000.0)|(1000.0,2000.0,3100.0)
```

### 3. 检测的差异类型
- **截面规格**：构件的截面型号
- **材质**：构件材质
- **长度**：构件长度（容差1mm）
- **重量**：构件重量（容差0.1kg）
- **位置**：构件重心坐标（容差0.5mm）

### 4. 使用方法
1. 在Tekla中选择两个要比较的构件或零件
2. 运行改进后的比较工具
3. 点击"比较"按钮
4. 在"差异数据"列中查看具体的数值差异

### 5. 技术改进
- **移除硬编码文本**：删除了所有"位置差异"、"part position"等硬编码描述
- **统一差异显示**：主构件和次构件都使用相同的差异显示格式
- **完整比较逻辑**：无论构件位置是否相同，都会显示详细的差异数据
- **空值处理**：当没有差异时，差异列显示为空

### 6. 优势
- **直观显示**：直接看到具体数值，无需额外解释
- **快速识别**：用分隔符清楚区分两个构件的对应数据
- **详细信息**：包含位置、尺寸、材质等多维度比较
- **焊接差异识别**：通过位置坐标差异可以识别焊接位置不同
- **一致性**：所有差异都使用统一的显示格式

### 7. 实际效果
现在当您比较两个P2构件时，不会再看到"位置差异"这样的描述文字，而是直接看到：
```
HW200x200x8x12|HW200x200x10x16 ; (1000.0,2000.0,3000.0)|(1000.0,2000.0,3100.0)
```

这样您就可以快速识别两个P2构件因焊接位置等因素造成的具体差异。
